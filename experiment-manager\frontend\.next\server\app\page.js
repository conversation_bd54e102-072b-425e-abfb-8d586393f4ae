/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fapp-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fbreadcrumb.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fapp-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fbreadcrumb.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/app-layout.tsx */ \"(ssr)/./src/components/layout/app-layout.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/breadcrumb.tsx */ \"(ssr)/./src/components/layout/breadcrumb.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGYXBwLWxheW91dC50c3gmbW9kdWxlcz0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJTJGc3JjJTJGY29tcG9uZW50cyUyRmxheW91dCUyRmJyZWFkY3J1bWIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leHBlcmltZW50LW1hbmFnZXItZnJvbnRlbmQvP2VlNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL2xheW91dC9hcHAtbGF5b3V0LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21udC9lLzAxLW1haW4vZnJhbWV3b3JrL2V4cGVyaW1lbnQtbWFuYWdlci9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9sYXlvdXQvYnJlYWRjcnVtYi50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fapp-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fbreadcrumb.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4cGVyaW1lbnQtbWFuYWdlci1mcm9udGVuZC8/ZmJiYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tbnQvZS8wMS1tYWluL2ZyYW1ld29yay9leHBlcmltZW50LW1hbmFnZXIvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/**\n * 实验管理系统首页 - 现代化内联样式版本\n * 与其他页面保持一致的设计风格\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalExperiments: 24,\n        runningExperiments: 4,\n        completedExperiments: 18,\n        successRate: 92\n    });\n    const [recentActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: \"深度学习模型训练\",\n            status: \"completed\",\n            time: \"2小时前完成\",\n            progress: 100\n        },\n        {\n            id: 2,\n            title: \"数据预处理实验\",\n            status: \"running\",\n            time: \"正在进行中\",\n            progress: 75\n        },\n        {\n            id: 3,\n            title: \"模型评估分析\",\n            status: \"pending\",\n            time: \"等待开始\",\n            progress: 25\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: \"2rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    gap: \"1.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"0.5rem\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.75rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        padding: \"0.75rem\",\n                                        background: \"linear-gradient(to right, #3b82f6, #8b5cf6)\",\n                                        borderRadius: \"0.75rem\",\n                                        boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        style: {\n                                            height: \"2rem\",\n                                            width: \"2rem\",\n                                            color: \"white\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                fontSize: \"clamp(1.875rem, 4vw, 2.25rem)\",\n                                                fontWeight: \"bold\",\n                                                background: \"linear-gradient(to right, #2563eb, #8b5cf6)\",\n                                                WebkitBackgroundClip: \"text\",\n                                                backgroundClip: \"text\",\n                                                WebkitTextFillColor: \"transparent\",\n                                                color: \"transparent\",\n                                                margin: 0\n                                            },\n                                            children: \"实验管理系统\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#6b7280\",\n                                                margin: \"0.25rem 0 0 0\"\n                                            },\n                                            children: '基于\"实验即契约\"理念的科研管理平台'\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"0.75rem\",\n                            marginLeft: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/experiments/new\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                style: {\n                                    background: \"linear-gradient(to right, #2563eb, #8b5cf6)\",\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"0.5rem\",\n                                    padding: \"0.5rem 1rem\",\n                                    fontSize: \"0.875rem\",\n                                    fontWeight: \"600\",\n                                    cursor: \"pointer\",\n                                    boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                                    transition: \"all 0.3s ease\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\",\n                                    textDecoration: \"none\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = \"linear-gradient(to right, #1d4ed8, #7c3aed)\";\n                                    e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"linear-gradient(to right, #2563eb, #8b5cf6)\";\n                                    e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        style: {\n                                            height: \"1rem\",\n                                            width: \"1rem\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"创建实验\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                    gap: \"1.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(239, 246, 255, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"1.5rem\",\n                            border: \"1px solid rgba(147, 197, 253, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                            transition: \"all 0.3s ease\",\n                            cursor: \"pointer\"\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#2563eb\",\n                                                fontSize: \"0.875rem\",\n                                                fontWeight: \"500\",\n                                                margin: \"0 0 0.5rem 0\"\n                                            },\n                                            children: \"总实验数\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"bold\",\n                                                color: \"#1e3a8a\",\n                                                margin: 0\n                                            },\n                                            children: stats.totalExperiments\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    style: {\n                                        height: \"2rem\",\n                                        width: \"2rem\",\n                                        color: \"#3b82f6\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(240, 253, 244, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"1.5rem\",\n                            border: \"1px solid rgba(134, 239, 172, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                            transition: \"all 0.3s ease\",\n                            cursor: \"pointer\"\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#059669\",\n                                                fontSize: \"0.875rem\",\n                                                fontWeight: \"500\",\n                                                margin: \"0 0 0.5rem 0\"\n                                            },\n                                            children: \"已完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"bold\",\n                                                color: \"#064e3b\",\n                                                margin: 0\n                                            },\n                                            children: stats.completedExperiments\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    style: {\n                                        height: \"2rem\",\n                                        width: \"2rem\",\n                                        color: \"#10b981\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255, 251, 235, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"1.5rem\",\n                            border: \"1px solid rgba(253, 186, 116, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                            transition: \"all 0.3s ease\",\n                            cursor: \"pointer\"\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#d97706\",\n                                                fontSize: \"0.875rem\",\n                                                fontWeight: \"500\",\n                                                margin: \"0 0 0.5rem 0\"\n                                            },\n                                            children: \"进行中\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"bold\",\n                                                color: \"#92400e\",\n                                                margin: 0\n                                            },\n                                            children: stats.runningExperiments\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    style: {\n                                        height: \"2rem\",\n                                        width: \"2rem\",\n                                        color: \"#f59e0b\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(250, 245, 255, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"1.5rem\",\n                            border: \"1px solid rgba(196, 181, 253, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                            transition: \"all 0.3s ease\",\n                            cursor: \"pointer\"\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#7c3aed\",\n                                                fontSize: \"0.875rem\",\n                                                fontWeight: \"500\",\n                                                margin: \"0 0 0.5rem 0\"\n                                            },\n                                            children: \"成功率\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"bold\",\n                                                color: \"#581c87\",\n                                                margin: 0\n                                            },\n                                            children: [\n                                                stats.successRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    style: {\n                                        height: \"2rem\",\n                                        width: \"2rem\",\n                                        color: \"#8b5cf6\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"2fr 1fr\",\n                    gap: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255, 255, 255, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"2rem\",\n                            border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"1rem\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"0.75rem\",\n                                            background: \"linear-gradient(to right, #3b82f6, #8b5cf6)\",\n                                            borderRadius: \"0.75rem\",\n                                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            style: {\n                                                height: \"1.5rem\",\n                                                width: \"1.5rem\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            fontWeight: \"bold\",\n                                            background: \"linear-gradient(to right, #2563eb, #8b5cf6)\",\n                                            WebkitBackgroundClip: \"text\",\n                                            backgroundClip: \"text\",\n                                            WebkitTextFillColor: \"transparent\",\n                                            color: \"transparent\",\n                                            margin: 0\n                                        },\n                                        children: \"快速入门\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/experiments/new\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: \"rgba(239, 246, 255, 0.8)\",\n                                                borderRadius: \"0.75rem\",\n                                                padding: \"1.5rem\",\n                                                border: \"1px solid rgba(147, 197, 253, 0.5)\",\n                                                transition: \"all 0.3s ease\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = \"rgba(219, 234, 254, 0.8)\";\n                                                e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = \"rgba(239, 246, 255, 0.8)\";\n                                                e.currentTarget.style.boxShadow = \"none\";\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"1rem\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.5rem\",\n                                                            width: \"1.5rem\",\n                                                            color: \"#3b82f6\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                style: {\n                                                                    fontSize: \"1.125rem\",\n                                                                    fontWeight: \"600\",\n                                                                    color: \"#1e40af\",\n                                                                    margin: \"0 0 0.25rem 0\"\n                                                                },\n                                                                children: \"创建第一个实验\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    color: \"#6b7280\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    margin: 0\n                                                                },\n                                                                children: \"开始你的科研之旅，记录实验假设和目标\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.25rem\",\n                                                            width: \"1.25rem\",\n                                                            color: \"#3b82f6\",\n                                                            marginLeft: \"auto\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/experiments\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: \"rgba(240, 253, 244, 0.8)\",\n                                                borderRadius: \"0.75rem\",\n                                                padding: \"1.5rem\",\n                                                border: \"1px solid rgba(134, 239, 172, 0.5)\",\n                                                transition: \"all 0.3s ease\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = \"rgba(220, 252, 231, 0.8)\";\n                                                e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = \"rgba(240, 253, 244, 0.8)\";\n                                                e.currentTarget.style.boxShadow = \"none\";\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"1rem\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.5rem\",\n                                                            width: \"1.5rem\",\n                                                            color: \"#10b981\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                style: {\n                                                                    fontSize: \"1.125rem\",\n                                                                    fontWeight: \"600\",\n                                                                    color: \"#065f46\",\n                                                                    margin: \"0 0 0.25rem 0\"\n                                                                },\n                                                                children: \"查看实验数据\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    color: \"#6b7280\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    margin: 0\n                                                                },\n                                                                children: \"分析实验结果，发现数据中的规律和洞察\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.25rem\",\n                                                            width: \"1.25rem\",\n                                                            color: \"#10b981\",\n                                                            marginLeft: \"auto\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/retrospectives\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: \"rgba(250, 245, 255, 0.8)\",\n                                                borderRadius: \"0.75rem\",\n                                                padding: \"1.5rem\",\n                                                border: \"1px solid rgba(196, 181, 253, 0.5)\",\n                                                transition: \"all 0.3s ease\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = \"rgba(243, 232, 255, 0.8)\";\n                                                e.currentTarget.style.boxShadow = \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = \"rgba(250, 245, 255, 0.8)\";\n                                                e.currentTarget.style.boxShadow = \"none\";\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"1rem\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.5rem\",\n                                                            width: \"1.5rem\",\n                                                            color: \"#8b5cf6\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                style: {\n                                                                    fontSize: \"1.125rem\",\n                                                                    fontWeight: \"600\",\n                                                                    color: \"#581c87\",\n                                                                    margin: \"0 0 0.25rem 0\"\n                                                                },\n                                                                children: \"进行实验复盘\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    color: \"#6b7280\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    margin: 0\n                                                                },\n                                                                children: \"深度反思实验过程，提炼经验和教训\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.25rem\",\n                                                            width: \"1.25rem\",\n                                                            color: \"#8b5cf6\",\n                                                            marginLeft: \"auto\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255, 255, 255, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"2rem\",\n                            border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"1rem\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"0.75rem\",\n                                            background: \"linear-gradient(to right, #8b5cf6, #ec4899)\",\n                                            borderRadius: \"0.75rem\",\n                                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            style: {\n                                                height: \"1.5rem\",\n                                                width: \"1.5rem\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            fontWeight: \"bold\",\n                                            background: \"linear-gradient(to right, #8b5cf6, #ec4899)\",\n                                            WebkitBackgroundClip: \"text\",\n                                            backgroundClip: \"text\",\n                                            WebkitTextFillColor: \"transparent\",\n                                            color: \"transparent\",\n                                            margin: 0\n                                        },\n                                        children: \"最近活动\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1rem\"\n                                },\n                                children: recentActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: activity.status === \"completed\" ? \"rgba(240, 253, 244, 0.8)\" : activity.status === \"running\" ? \"rgba(239, 246, 255, 0.8)\" : \"rgba(255, 251, 235, 0.8)\",\n                                            borderRadius: \"0.75rem\",\n                                            padding: \"1rem\",\n                                            border: `1px solid ${activity.status === \"completed\" ? \"rgba(134, 239, 172, 0.5)\" : activity.status === \"running\" ? \"rgba(147, 197, 253, 0.5)\" : \"rgba(253, 186, 116, 0.5)\"}`,\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"0.75rem\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            padding: \"0.5rem\",\n                                                            background: activity.status === \"completed\" ? \"#10b981\" : activity.status === \"running\" ? \"#3b82f6\" : \"#f59e0b\",\n                                                            borderRadius: \"0.5rem\"\n                                                        },\n                                                        children: [\n                                                            activity.status === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                style: {\n                                                                    height: \"1rem\",\n                                                                    width: \"1rem\",\n                                                                    color: \"white\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            activity.status === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                style: {\n                                                                    height: \"1rem\",\n                                                                    width: \"1rem\",\n                                                                    color: \"white\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            activity.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                style: {\n                                                                    height: \"1rem\",\n                                                                    width: \"1rem\",\n                                                                    color: \"white\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 55\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            flex: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    fontWeight: \"600\",\n                                                                    color: \"#374151\",\n                                                                    margin: \"0 0 0.25rem 0\",\n                                                                    fontSize: \"0.875rem\"\n                                                                },\n                                                                children: activity.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    fontSize: \"0.75rem\",\n                                                                    color: activity.status === \"completed\" ? \"#059669\" : activity.status === \"running\" ? \"#2563eb\" : \"#d97706\",\n                                                                    margin: 0\n                                                                },\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"0.25rem\",\n                                                    background: activity.status === \"completed\" ? \"#d1fae5\" : activity.status === \"running\" ? \"#dbeafe\" : \"#fed7aa\",\n                                                    borderRadius: \"0.125rem\",\n                                                    marginTop: \"0.75rem\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: `${activity.progress}%`,\n                                                        height: \"100%\",\n                                                        background: activity.status === \"completed\" ? \"#10b981\" : activity.status === \"running\" ? \"#3b82f6\" : \"#f59e0b\",\n                                                        borderRadius: \"0.125rem\",\n                                                        transition: \"width 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, activity.id, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"1.5rem\",\n                                    textAlign: \"center\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/experiments\",\n                                    style: {\n                                        color: \"#8b5cf6\",\n                                        fontSize: \"0.875rem\",\n                                        fontWeight: \"500\",\n                                        textDecoration: \"none\",\n                                        display: \"inline-flex\",\n                                        alignItems: \"center\",\n                                        gap: \"0.5rem\"\n                                    },\n                                    children: [\n                                        \"查看所有活动\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            style: {\n                                                height: \"1rem\",\n                                                width: \"1rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/app-layout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/app-layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/**\n * 实验管理系统布局组件 - 与现有页面风格完全一致\n * 采用内联样式，玻璃态效果，与 experiments 页面设计语言统一\n */ /* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\n// 导航菜单配置 - 与现有页面风格一致\nconst navigationItems = [\n    {\n        name: \"仪表板\",\n        href: \"/\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#2563eb\"\n    },\n    {\n        name: \"实验管理\",\n        href: \"/experiments\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"#8b5cf6\"\n    },\n    {\n        name: \"创建实验\",\n        href: \"/experiments/new\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"#059669\"\n    },\n    {\n        name: \"系统设置\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"#d97706\"\n    }\n];\nfunction AppLayout({ children }) {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 检测当前页面类型\n    const isExperimentDetailPage = pathname.match(/^\\/experiments\\/[^\\/]+$/);\n    const isExperimentSubPage = pathname.match(/^\\/experiments\\/[^\\/]+\\//);\n    const experimentId = pathname.match(/^\\/experiments\\/([^\\/]+)/)?.[1];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    zIndex: 50,\n                    background: \"rgba(255, 255, 255, 0.9)\",\n                    backdropFilter: \"blur(12px)\",\n                    borderBottom: \"1px solid rgba(229, 231, 235, 0.5)\",\n                    boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"1400px\",\n                        margin: \"0 auto\",\n                        padding: \"0 1rem\",\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        height: \"4rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\",\n                                    textDecoration: \"none\",\n                                    transition: \"all 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = \"scale(1.02)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = \"scale(1)\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"0.75rem\",\n                                            background: \"linear-gradient(to right, #3b82f6, #8b5cf6)\",\n                                            borderRadius: \"0.75rem\",\n                                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            style: {\n                                                height: \"1.5rem\",\n                                                width: \"1.5rem\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"1.25rem\",\n                                                    fontWeight: \"bold\",\n                                                    background: \"linear-gradient(to right, #2563eb, #8b5cf6)\",\n                                                    WebkitBackgroundClip: \"text\",\n                                                    backgroundClip: \"text\",\n                                                    WebkitTextFillColor: \"transparent\",\n                                                    color: \"transparent\"\n                                                },\n                                                children: \"实验管理系统\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"0.75rem\",\n                                                    color: \"#6b7280\"\n                                                },\n                                                children: \"科研实验管理平台\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.5rem\"\n                            },\n                            children: [\n                                (isExperimentDetailPage || isExperimentSubPage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"0.5rem\",\n                                        marginRight: \"1rem\",\n                                        padding: \"0.5rem 0.75rem\",\n                                        background: \"rgba(255, 255, 255, 0.6)\",\n                                        borderRadius: \"0.5rem\",\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/experiments\",\n                                            style: {\n                                                color: \"#6b7280\",\n                                                textDecoration: \"none\",\n                                                transition: \"color 0.3s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.color = \"#374151\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.color = \"#6b7280\";\n                                            },\n                                            children: \"实验管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#d1d5db\"\n                                            },\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#374151\",\n                                                fontWeight: \"500\"\n                                            },\n                                            children: [\n                                                \"实验 #\",\n                                                experimentId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                navigationItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = pathname === item.href || item.href !== \"/\" && pathname.startsWith(item.href);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.75rem 1rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            transition: \"all 0.3s ease\",\n                                            background: isActive ? \"linear-gradient(to right, #2563eb, #8b5cf6)\" : \"transparent\",\n                                            color: isActive ? \"white\" : \"#374151\",\n                                            boxShadow: isActive ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\" : \"none\",\n                                            transform: isActive ? \"scale(1.05)\" : \"scale(1)\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!isActive) {\n                                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.7)\";\n                                                e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                                e.currentTarget.style.transform = \"scale(1.02)\";\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!isActive) {\n                                                e.currentTarget.style.background = \"transparent\";\n                                                e.currentTarget.style.boxShadow = \"none\";\n                                                e.currentTarget.style.transform = \"scale(1)\";\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                style: {\n                                                    height: \"1rem\",\n                                                    width: \"1rem\",\n                                                    color: isActive ? \"white\" : item.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.75rem\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\",\n                                    background: \"rgba(255, 255, 255, 0.7)\",\n                                    border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                                    borderRadius: \"0.5rem\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(243, 244, 246, 0.8)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.7)\";\n                                },\n                                className: \"md:hidden\",\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    style: {\n                                        height: \"1.25rem\",\n                                        width: \"1.25rem\",\n                                        color: \"#6b7280\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    style: {\n                                        height: \"1.25rem\",\n                                        width: \"1.25rem\",\n                                        color: \"#6b7280\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: \"4rem\",\n                    left: 0,\n                    right: 0,\n                    zIndex: 40,\n                    background: \"rgba(255, 255, 255, 0.9)\",\n                    backdropFilter: \"blur(12px)\",\n                    borderBottom: \"1px solid rgba(229, 231, 235, 0.5)\",\n                    padding: \"1rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"0.5rem\"\n                    },\n                    children: [\n                        (isExperimentDetailPage || isExperimentSubPage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.5rem\",\n                                padding: \"0.75rem\",\n                                background: \"rgba(239, 246, 255, 0.5)\",\n                                borderRadius: \"0.5rem\",\n                                marginBottom: \"0.75rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    style: {\n                                        height: \"1rem\",\n                                        width: \"1rem\",\n                                        color: \"#2563eb\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"0.875rem\",\n                                        color: \"#2563eb\",\n                                        fontWeight: \"500\"\n                                    },\n                                    children: [\n                                        \"实验 #\",\n                                        experimentId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 15\n                        }, this),\n                        navigationItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = pathname === item.href || item.href !== \"/\" && pathname.startsWith(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\",\n                                    padding: \"0.75rem 1rem\",\n                                    borderRadius: \"0.75rem\",\n                                    fontSize: \"0.875rem\",\n                                    fontWeight: \"500\",\n                                    textDecoration: \"none\",\n                                    transition: \"all 0.3s ease\",\n                                    background: isActive ? \"linear-gradient(to right, #2563eb, #8b5cf6)\" : \"transparent\",\n                                    color: isActive ? \"white\" : \"#6b7280\",\n                                    boxShadow: isActive ? \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                },\n                                onClick: ()=>setMobileMenuOpen(false),\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.currentTarget.style.background = \"rgba(255, 255, 255, 0.5)\";\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.currentTarget.style.background = \"transparent\";\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        style: {\n                                            height: \"1.25rem\",\n                                            width: \"1.25rem\",\n                                            color: isActive ? \"white\" : item.color\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    paddingTop: \"4rem\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"1400px\",\n                        margin: \"0 auto\",\n                        padding: \"2rem\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/app-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/breadcrumb.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/breadcrumb.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ Breadcrumb),\n/* harmony export */   PageHeader: () => (/* binding */ PageHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/**\n * 面包屑导航组件\n * 提供页面层级导航，帮助用户了解当前位置\n */ /* __next_internal_client_entry_do_not_use__ Breadcrumb,PageHeader auto */ \n\n\n\n\n\n// 根据路径自动生成面包屑的映射\nconst pathToBreadcrumb = {\n    \"/\": [\n        {\n            label: \"首页\",\n            current: true\n        }\n    ],\n    \"/experiments\": [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"实验列表\",\n            current: true\n        }\n    ],\n    \"/experiments/new\": [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"实验列表\",\n            href: \"/experiments\"\n        },\n        {\n            label: \"创建实验\",\n            current: true\n        }\n    ],\n    \"/settings\": [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"设置\",\n            current: true\n        }\n    ]\n};\n// 动态路径处理函数\nfunction generateBreadcrumbFromPath(pathname) {\n    // 检查是否是实验详情页面\n    const experimentDetailMatch = pathname.match(/^\\/experiments\\/([^\\/]+)$/);\n    if (experimentDetailMatch) {\n        const experimentId = experimentDetailMatch[1];\n        return [\n            {\n                label: \"首页\",\n                href: \"/\"\n            },\n            {\n                label: \"实验列表\",\n                href: \"/experiments\"\n            },\n            {\n                label: `实验 ${experimentId.slice(0, 8)}...`,\n                current: true\n            }\n        ];\n    }\n    // 检查是否是实验复盘页面\n    const experimentReviewMatch = pathname.match(/^\\/experiments\\/([^\\/]+)\\/review$/);\n    if (experimentReviewMatch) {\n        const experimentId = experimentReviewMatch[1];\n        return [\n            {\n                label: \"首页\",\n                href: \"/\"\n            },\n            {\n                label: \"实验列表\",\n                href: \"/experiments\"\n            },\n            {\n                label: `实验 ${experimentId.slice(0, 8)}...`,\n                href: `/experiments/${experimentId}`\n            },\n            {\n                label: \"复盘\",\n                current: true\n            }\n        ];\n    }\n    // 检查是否是实验编辑页面\n    const experimentEditMatch = pathname.match(/^\\/experiments\\/([^\\/]+)\\/edit$/);\n    if (experimentEditMatch) {\n        const experimentId = experimentEditMatch[1];\n        return [\n            {\n                label: \"首页\",\n                href: \"/\"\n            },\n            {\n                label: \"实验列表\",\n                href: \"/experiments\"\n            },\n            {\n                label: `实验 ${experimentId.slice(0, 8)}...`,\n                href: `/experiments/${experimentId}`\n            },\n            {\n                label: \"编辑\",\n                current: true\n            }\n        ];\n    }\n    // 返回静态映射或默认面包屑\n    return pathToBreadcrumb[pathname] || [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"未知页面\",\n            current: true\n        }\n    ];\n}\nfunction Breadcrumb({ items, className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 使用传入的 items 或根据路径自动生成\n    const breadcrumbItems = items || generateBreadcrumbFromPath(pathname);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center space-x-1 text-sm text-gray-500\", className),\n        \"aria-label\": \"面包屑导航\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            breadcrumbItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                    children: [\n                        index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        item.current ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-900\",\n                            \"aria-current\": \"page\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href || \"#\",\n                            className: \"hover:text-gray-700 transition-colors\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction PageHeader({ title, description, action, breadcrumbItems, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-6\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Breadcrumb, {\n                items: breadcrumbItems,\n                className: \"mb-2\"\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: action\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/**\n * 工具函数\n * 提供常用的工具函数，包括类名合并、格式化等\n */ \n\n/**\n * 合并 Tailwind CSS 类名\n * 使用 clsx 和 tailwind-merge 来智能合并类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化文件大小\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * 生成随机ID\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 深拷贝对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 检查是否为空值\n */ function isEmpty(value) {\n    if (value == null) return true;\n    if (typeof value === \"string\") return value.trim().length === 0;\n    if (Array.isArray(value)) return value.length === 0;\n    if (typeof value === \"object\") return Object.keys(value).length === 0;\n    return false;\n}\n/**\n * 首字母大写\n */ function capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\n/**\n * 截断文本\n */ function truncate(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + \"...\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"347bc58f1c86\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8xMjczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzQ3YmM1OGYxYzg2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/layout */ \"(rsc)/./src/components/layout/index.ts\");\n\n\n\n\nconst metadata = {\n    title: \"实验管理系统\",\n    description: '基于\"实验即契约\"理念的科研实验管理平台'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_2__.AppLayout, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzBCO0FBSXpDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MseURBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBBcHBMYXlvdXQgfSBmcm9tICcuLi9jb21wb25lbnRzL2xheW91dCdcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ+WunumqjOeuoeeQhuezu+e7nycsXG4gIGRlc2NyaXB0aW9uOiAn5Z+65LqOXCLlrp7pqozljbPlpZHnuqZcIueQhuW/teeahOenkeeglOWunumqjOeuoeeQhuW5s+WPsCcsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXBwTGF5b3V0PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9BcHBMYXlvdXQ+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBcHBMYXlvdXQiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $$typeof: () => (/* binding */ $$typeof),\n/* harmony export */   __esModule: () => (/* binding */ __esModule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/app-layout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/app-layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx#AppLayout`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/app-layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/breadcrumb.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/breadcrumb.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ e0),\n/* harmony export */   PageHeader: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx#Breadcrumb`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx#PageHeader`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/breadcrumb.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/index.ts":
/*!****************************************!*\
  !*** ./src/components/layout/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* reexport safe */ _app_layout__WEBPACK_IMPORTED_MODULE_0__.AppLayout),\n/* harmony export */   Breadcrumb: () => (/* reexport safe */ _breadcrumb__WEBPACK_IMPORTED_MODULE_1__.Breadcrumb),\n/* harmony export */   PageHeader: () => (/* reexport safe */ _breadcrumb__WEBPACK_IMPORTED_MODULE_1__.PageHeader)\n/* harmony export */ });\n/* harmony import */ var _app_layout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app-layout */ \"(rsc)/./src/components/layout/app-layout.tsx\");\n/* harmony import */ var _breadcrumb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./breadcrumb */ \"(rsc)/./src/components/layout/breadcrumb.tsx\");\n/**\n * 布局组件导出\n * 统一导出所有布局相关的组件\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFdUM7QUFDYSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4cGVyaW1lbnQtbWFuYWdlci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL2xheW91dC9pbmRleC50cz9hMTQ1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5biD5bGA57uE5Lu25a+85Ye6XG4gKiDnu5/kuIDlr7zlh7rmiYDmnInluIPlsYDnm7jlhbPnmoTnu4Tku7ZcbiAqL1xuXG5leHBvcnQgeyBBcHBMYXlvdXQgfSBmcm9tICcuL2FwcC1sYXlvdXQnXG5leHBvcnQgeyBCcmVhZGNydW1iLCBQYWdlSGVhZGVyIH0gZnJvbSAnLi9icmVhZGNydW1iJ1xuIl0sIm5hbWVzIjpbIkFwcExheW91dCIsIkJyZWFkY3J1bWIiLCJQYWdlSGVhZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/index.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();