/**
 * 实验管理系统首页 - 现代化内联样式版本
 * 与其他页面保持一致的设计风格
 */

'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  FlaskConical,
  Plus,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  ArrowRight,
  Zap,
  BarChart3,
  Target
} from 'lucide-react'

export default function Home() {
  const [stats, setStats] = useState({
    totalExperiments: 24,
    runningExperiments: 4,
    completedExperiments: 18,
    successRate: 92
  })

  const [recentActivities] = useState([
    {
      id: 1,
      title: "深度学习模型训练",
      status: "completed",
      time: "2小时前完成",
      progress: 100
    },
    {
      id: 2,
      title: "数据预处理实验",
      status: "running",
      time: "正在进行中",
      progress: 75
    },
    {
      id: 3,
      title: "模型评估分析",
      status: "pending",
      time: "等待开始",
      progress: 25
    }
  ])

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      {/* 页面标题 */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '1.5rem'
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
              borderRadius: '0.75rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              <FlaskConical style={{ height: '2rem', width: '2rem', color: 'white' }} />
            </div>
            <div>
              <h1 style={{
                fontSize: 'clamp(1.875rem, 4vw, 2.25rem)',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                color: 'transparent',
                margin: 0
              }}>
                实验管理系统
              </h1>
              <p style={{ color: '#6b7280', margin: '0.25rem 0 0 0' }}>基于"实验即契约"理念的科研管理平台</p>
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginLeft: 'auto' }}>
          <Link href="/experiments/new">
            <button
              style={{
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #7c3aed)';
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #8b5cf6)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
              }}
            >
              <Plus style={{ height: '1rem', width: '1rem' }} />
              创建实验
            </button>
          </Link>
        </div>
      </div>

      {/* 统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem'
      }}>
        {/* 总实验数 */}
        <div style={{
          background: 'rgba(239, 246, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '1.5rem',
          border: '1px solid rgba(147, 197, 253, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#2563eb', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                总实验数
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1e3a8a', margin: 0 }}>
                {stats.totalExperiments}
              </p>
            </div>
            <FlaskConical style={{ height: '2rem', width: '2rem', color: '#3b82f6' }} />
          </div>
        </div>

        {/* 已完成 */}
        <div style={{
          background: 'rgba(240, 253, 244, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '1.5rem',
          border: '1px solid rgba(134, 239, 172, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#059669', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                已完成
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#064e3b', margin: 0 }}>
                {stats.completedExperiments}
              </p>
            </div>
            <CheckCircle style={{ height: '2rem', width: '2rem', color: '#10b981' }} />
          </div>
        </div>

        {/* 进行中 */}
        <div style={{
          background: 'rgba(255, 251, 235, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '1.5rem',
          border: '1px solid rgba(253, 186, 116, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#d97706', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                进行中
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#92400e', margin: 0 }}>
                {stats.runningExperiments}
              </p>
            </div>
            <Activity style={{ height: '2rem', width: '2rem', color: '#f59e0b' }} />
          </div>
        </div>

        {/* 成功率 */}
        <div style={{
          background: 'rgba(250, 245, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '1.5rem',
          border: '1px solid rgba(196, 181, 253, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#7c3aed', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                成功率
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#581c87', margin: 0 }}>
                {stats.successRate}%
              </p>
            </div>
            <TrendingUp style={{ height: '2rem', width: '2rem', color: '#8b5cf6' }} />
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '2rem'
      }}>
        {/* 快速入门 */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '2rem',
          border: '1px solid rgba(229, 231, 235, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1.5rem' }}>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
              borderRadius: '0.75rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              <Zap style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
            </div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              color: 'transparent',
              margin: 0
            }}>
              快速入门
            </h2>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <Link href="/experiments/new" style={{ textDecoration: 'none' }}>
              <div style={{
                background: 'rgba(239, 246, 255, 0.8)',
                borderRadius: '0.75rem',
                padding: '1.5rem',
                border: '1px solid rgba(147, 197, 253, 0.5)',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(219, 234, 254, 0.8)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(239, 246, 255, 0.8)';
                e.currentTarget.style.boxShadow = 'none';
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                  <Plus style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6' }} />
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1e40af', margin: '0 0 0.25rem 0' }}>
                      创建第一个实验
                    </h3>
                    <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                      开始你的科研之旅，记录实验假设和目标
                    </p>
                  </div>
                  <ArrowRight style={{ height: '1.25rem', width: '1.25rem', color: '#3b82f6', marginLeft: 'auto' }} />
                </div>
              </div>
            </Link>

            <Link href="/experiments" style={{ textDecoration: 'none' }}>
              <div style={{
                background: 'rgba(240, 253, 244, 0.8)',
                borderRadius: '0.75rem',
                padding: '1.5rem',
                border: '1px solid rgba(134, 239, 172, 0.5)',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(220, 252, 231, 0.8)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(240, 253, 244, 0.8)';
                e.currentTarget.style.boxShadow = 'none';
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                  <BarChart3 style={{ height: '1.5rem', width: '1.5rem', color: '#10b981' }} />
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#065f46', margin: '0 0 0.25rem 0' }}>
                      查看实验数据
                    </h3>
                    <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                      分析实验结果，发现数据中的规律和洞察
                    </p>
                  </div>
                  <ArrowRight style={{ height: '1.25rem', width: '1.25rem', color: '#10b981', marginLeft: 'auto' }} />
                </div>
              </div>
            </Link>

            <Link href="/retrospectives" style={{ textDecoration: 'none' }}>
              <div style={{
                background: 'rgba(250, 245, 255, 0.8)',
                borderRadius: '0.75rem',
                padding: '1.5rem',
                border: '1px solid rgba(196, 181, 253, 0.5)',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(243, 232, 255, 0.8)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(250, 245, 255, 0.8)';
                e.currentTarget.style.boxShadow = 'none';
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                  <Target style={{ height: '1.5rem', width: '1.5rem', color: '#8b5cf6' }} />
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#581c87', margin: '0 0 0.25rem 0' }}>
                      进行实验复盘
                    </h3>
                    <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                      深度反思实验过程，提炼经验和教训
                    </p>
                  </div>
                  <ArrowRight style={{ height: '1.25rem', width: '1.25rem', color: '#8b5cf6', marginLeft: 'auto' }} />
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* 最近活动 */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '2rem',
          border: '1px solid rgba(229, 231, 235, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1.5rem' }}>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #8b5cf6, #ec4899)',
              borderRadius: '0.75rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              <Activity style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
            </div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #8b5cf6, #ec4899)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              color: 'transparent',
              margin: 0
            }}>
              最近活动
            </h2>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {recentActivities.map((activity) => (
              <div key={activity.id} style={{
                background: activity.status === 'completed' ? 'rgba(240, 253, 244, 0.8)' :
                           activity.status === 'running' ? 'rgba(239, 246, 255, 0.8)' :
                           'rgba(255, 251, 235, 0.8)',
                borderRadius: '0.75rem',
                padding: '1rem',
                border: `1px solid ${
                  activity.status === 'completed' ? 'rgba(134, 239, 172, 0.5)' :
                  activity.status === 'running' ? 'rgba(147, 197, 253, 0.5)' :
                  'rgba(253, 186, 116, 0.5)'
                }`,
                transition: 'all 0.3s ease'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <div style={{
                    padding: '0.5rem',
                    background: activity.status === 'completed' ? '#10b981' :
                               activity.status === 'running' ? '#3b82f6' :
                               '#f59e0b',
                    borderRadius: '0.5rem'
                  }}>
                    {activity.status === 'completed' && <CheckCircle style={{ height: '1rem', width: '1rem', color: 'white' }} />}
                    {activity.status === 'running' && <Activity style={{ height: '1rem', width: '1rem', color: 'white' }} />}
                    {activity.status === 'pending' && <Clock style={{ height: '1rem', width: '1rem', color: 'white' }} />}
                  </div>
                  <div style={{ flex: 1 }}>
                    <p style={{ fontWeight: '600', color: '#374151', margin: '0 0 0.25rem 0', fontSize: '0.875rem' }}>
                      {activity.title}
                    </p>
                    <p style={{
                      fontSize: '0.75rem',
                      color: activity.status === 'completed' ? '#059669' :
                             activity.status === 'running' ? '#2563eb' :
                             '#d97706',
                      margin: 0
                    }}>
                      {activity.time}
                    </p>
                  </div>
                </div>
                <div style={{
                  width: '100%',
                  height: '0.25rem',
                  background: activity.status === 'completed' ? '#d1fae5' :
                             activity.status === 'running' ? '#dbeafe' :
                             '#fed7aa',
                  borderRadius: '0.125rem',
                  marginTop: '0.75rem'
                }}>
                  <div style={{
                    width: `${activity.progress}%`,
                    height: '100%',
                    background: activity.status === 'completed' ? '#10b981' :
                               activity.status === 'running' ? '#3b82f6' :
                               '#f59e0b',
                    borderRadius: '0.125rem',
                    transition: 'width 0.3s ease'
                  }}></div>
                </div>
              </div>
            ))}
          </div>

          <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
            <Link href="/experiments" style={{
              color: '#8b5cf6',
              fontSize: '0.875rem',
              fontWeight: '500',
              textDecoration: 'none',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              查看所有活动
              <ArrowRight style={{ height: '1rem', width: '1rem' }} />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
